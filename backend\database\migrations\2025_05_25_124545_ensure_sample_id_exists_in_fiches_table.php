<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('fiches', function (Blueprint $table) {
            // Check if sample_id column doesn't exist and add it
            if (!Schema::hasColumn('fiches', 'sample_id')) {
                $table->unsignedBigInteger('sample_id')->nullable();
                $table->foreign('sample_id')->references('id')->on('samples')->onDelete('set null');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('fiches', function (Blueprint $table) {
            //
        });
    }
};
